<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { SparklesIcon } from "lucide-vue-next";
import ParalleloScan from "@/components/ParalleloScan.vue";

const stages = [
  "Understanding your query",
  "Getting the context",
  "Exploring different angles",
  "Organizing my thoughts",
  "Crafting the process",
];
const currentStage = ref(stages[0]);
let timer: ReturnType<typeof setTimeout> | null = null;

// duration 3-5秒
const duration = ref(Math.random() * 2000 + 3000);

const nextStage = () => {
  const otherStages = stages.filter((s) => s !== currentStage.value);
  const randomIndex = Math.floor(Math.random() * otherStages.length);
  currentStage.value = otherStages[randomIndex];

  const delay = Math.random() * 2000 + 2000;
  duration.value = Math.random() * 2000 + 3000;
  timer = setTimeout(nextStage, delay);
};

onMounted(() => {
  const delay = Math.random() * 2000 + 2000;
  duration.value = Math.random() * 2000 + 3000;
  timer = setTimeout(nextStage, delay);
});

onUnmounted(() => {
  if (timer) clearTimeout(timer);
});
</script>

<template>
  <div class="flex items-center gap-2 my-4">
    <ParalleloScan :duration="`${duration / 1000}s`">
      <SparklesIcon class="h-4 w-4 animate-breath" />
      <span class="font-medium text-lg">{{ currentStage }}</span>
      <ClientOnly>
        <l-pinwheel
          size="16"
          speed="1.3"
          color="oklch(0.5613 0.0924 238.72)"
          class="dot-position"
        />
      </ClientOnly>
    </ParalleloScan>
  </div>
</template>

<style scoped>
.dot-position {
  position: absolute;
  bottom: 0.25rem;
  right: -1.5rem;
}
@keyframes breath {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

.animate-breath {
  animation: breath 1.6s ease-in-out infinite;
}
</style>
