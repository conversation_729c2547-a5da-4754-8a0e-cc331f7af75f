---
type: "always_apply"
---

Purpose

Follow these guardrails when generating code. Always produce design-focused, type-safe, maintainable code consistent with this stack.

Tech Stack
	•	Vue 3 + Nuxt 3 (pages/, server/api/ conventions)
	•	TypeScript only
	•	<script setup lang="ts"> + Composition API (no Options API)
	•	Pinia via @pinia/nuxt
	•	Tailwind CSS
	•	shadcn-vue via shadcn-nuxt
	•	Vite (Nuxt default)
	•	ESLint + Prettier for style rules only

Output Rules
	•	Deliver complete file contents with a top-line path comment.
	•	Provide only code and minimal rationale.
	•	Never output setup or run commands.
	•	Maintain consistent naming, file structure, and style.

Coding Conventions

Components
	•	Always use <script setup lang="ts"> with defineProps<T>() and defineEmits<T>().
	•	Extract logic into composables/.
	•	Use useFetch() or useAsyncData() with typing, loading, and error handling.
	•	Handle states: loading, empty, error, success.
	•	Ensure accessibility (aria-*, keyboard navigation).

Pinia
	•	Stores in stores/, named useXxxStore.
	•	Clear separation of state, getters, actions.
	•	Async actions typed and error-safe.
	•	No UI logic inside stores. Must support SSR.

Styling & UI
	•	Tailwind only for layout, spacing, and typography.
	•	Use shadcn-vue components for UI (Button, Input, Dialog, etc.).
	•	Follow Nuxt + shadcn-vue conventions for theming/dark mode.
	•	Use Tailwind’s default rounded classes, but avoid rounded-sm and rounded-md unless explicitly requested by the user.

Project Structure

/components/       # generic
/components/ui/    # shadcn-vue
/composables/      # logic
/server/api/       # endpoints
/stores/           # Pinia
/pages/            # routed pages
/types/            # shared types

Quality Requirements
	•	Full typing for props, composables, stores, API results.
	•	Logic in composables/stores, components handle UI only.
	•	Code must be testable and maintainable.
	•	Semantic naming, concise comments.
	•	Performance-aware (avoid unnecessary reactivity).

Data & Interaction
	•	Use useFetch() for page-level data with types and states.
	•	API endpoints return clean DTOs.
	•	Centralize data fetching in composables/stores to avoid duplication.
	•	Always represent loading/error states in UI.

Response Template

When asked to implement a feature, respond with:
	1.	Intent Summary — one sentence of the goal.
	2.	Design Suggestions — bullet points on structure and flow.
	3.	File List — file paths you will provide.
	4.	Code — full file contents with path comment.
	5.	Checklist — ≤6 review points (typing, accessibility, states, performance).

Rule: Always follow these guardrails. Never deviate from the defined stack or output installation instructions.